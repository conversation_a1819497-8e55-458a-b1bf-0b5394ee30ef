# 🧠 架構與設計理念說明

本專案為一個具備高度可擴充性與清晰模組劃分的伺服器管理與監控系統，採用了以下核心架構設計原則與模式：

---

## 📐 三層式架構（3-Tier Architecture）

| 層級       | 模組                          | 說明 |
|------------|-------------------------------|------|
| 表示層     | `index.js`                    | Lambda handler，負責事件處理與流程控制 |
| 應用層     | `LinuxHost.js`, `WindowsHost.js`, `HostFactory.js` | 根據主機類型執行任務、彙整回應 |
| 基礎設施層 | `SSHClient.js`, `DiscordNotifier.js`, 各種 tasks | 封裝與外部互動（如 SSH、Webhook、Shell） |

---

## 🧩 關鍵架構概念與設計模式

### ✅ 設定驅動設計（Configuration-Driven Design）

所有主機的登入資訊與要執行的任務，皆定義於 `serverList.json` 檔案中。

- 可藉由修改 JSON 檔案，即時擴增主機或任務，不需動到程式碼
- 屬於一種 **資料驅動的應用設計**，提高靈活性與部署效率

---

### ✅ Interface-Based Programming / 抽象繼承

所有主機類別（LinuxHost、WindowsHost）都繼承自 `BaseHost`：

- `BaseHost` 提供統一的 `execute()` 方法邏輯框架
- 實際差異（如 SSH 登入方式）由子類實作
- 屬於典型 **依賴抽象而非具體實作** 的 OOP 原則（符合 DIP）

---

### ✅ Factory Pattern（工廠模式）

`createHost(config)` 負責根據主機的 `type` 字串，動態實例化對應的 Host 類別：

- 封裝 Host 的建立邏輯
- 解耦 index 與具體主機類型
- 易於擴充更多作業系統支援（pfSense、macOS...）

---

### ✅ Strategy Pattern：Task 策略封裝

每個健康檢查被包裝為一個 Task 類別物件，並使用統一的 `run()` 方法執行，包含：

- `check(ssh)`：檢查邏輯
- `rule(value, args)`：狀態核對與錯誤判斷
- `recover(ssh)`：修復邏輯（可選）

---

### ✅ 抽象建構器 + 注入策略：Task.js 設計概念

`Task.js` 本身提供的是一個「高彈性建構器」：

```js
new Task({ name, args, check, rule, recover });
```

- **由各個 Task 模組注入 check / rule / recover 策略**
- 本質上是 **組合式策略注入物件**，類似 Functional Strategy Pattern
- 可輕易測試與覆寫任務邏輯，提升擴充與維護性

---

### ✅ Open-Closed Principle（開放封閉原則）

- 每一個 Task 模組彼此獨立，透過配置（`serverList.json`）決定執行
- 任務擴充只需新增 JS 檔與註冊，不需改動核心流程

---

### ✅ Dependency Injection（依賴注入）

- `notifier`、`errorNotifier` 由 `index.js` 主動注入
- 各 Task 或 SSHClient 不自行創建外部依賴，符合單一責任與可測性設計

---

### ✅ Loose Coupling（低耦合）

- `Task`、`SSHClient`、`Notifier`、`Host` 各模組彼此獨立，透過明確介面互動
- 可獨立替換通訊協定（如改用 Telnet）、通知方式（如 Email）

---

## 🔁 任務流程圖概念

```
[Lambda Trigger]
      ↓
[createHost(config)]
      ↓
[Host.execute()]
      ↓
[loadTasks() → Task.run()]
      ↓
[Task: check → rule → recover]
      ↓
[回傳結果 → index.js 統一通知或回應]
```

---

## 🔧 彈性擴充接口

| 模組             | 擴充方式                                  |
|------------------|-------------------------------------------|
| Task             | 新增 JS 檔，export factory function        |
| Notifier         | 繼承 BaseNotifier，實作 send()            |
| Host             | 繼承 BaseHost，實作特殊作業系統支援邏輯  |
| serverList.json  | 無需修改程式碼即可控制執行邏輯            |

---

## 📦 總結

本系統以 **三層式架構 + 策略注入 + 工廠模式 + DI** 為核心，實現：

- 高擴充性
- 低耦合
- 清晰分責
- 設定驅動自動化

適合任何需要跨機器、跨系統的彈性監控／維運任務。若搭配 Serverless 平台如 Lambda，更可實現極低成本的定時管理與即時回應。
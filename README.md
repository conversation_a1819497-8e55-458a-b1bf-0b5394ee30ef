# 🛠️ Auto Server Manager

以 AWS Lambda 為核心，對虛擬機（Linux / Windows）進行健康檢查、狀態匯報、故障自動修復與指令轉發的伺服器統一管理系統。

配合的管理網頁專案：[伺服器管理頁面](https://github.com/taipeitechmmslab/auto-Server-Manager-Web)

Lambda部屬位置：[autoServerManager](https://ap-northeast-1.console.aws.amazon.com/lambda/home?region=ap-northeast-1#/functions/autoServerManager?subtab=vpc&tab=monitoring)

CI/CD：[Jenkins](https://ci2.bluenet-ride.com/job/auto-Server-Manager/)

---

## 📁 專案架構

```
index.js                       # Lambda 進入點（AWS Lambda handler）
localTest.js                   # 本地測試進入點（可模擬 Lambda 執行）

functions/
├── core/                      # 主機抽象與任務執行核心
│   ├── BaseHost.js            # 抽象主機類別
│   ├── HostFactory.js         # 實例化主機類別
│   ├── LinuxHost.js           # Linux 虛擬機操作邏輯
│   ├── WindowsHost.js         # Windows 虛擬機操作邏輯
│   ├── tasks/                 # 所有 Task 任務模組
│   │   ├── Task.js            # Task 抽象定義
│   │   └── *Task.js           # 各種檢查任務（CheckDocker, CheckMongoDB...）
├── infra/                     # 對外基礎設施操作層
│   ├── ssh/
│   │   ├── SSHClient.js       # SSH 操作封裝
│   └── notifier/
│   │   ├── DiscordNotifier.js # Discord 通知
├── utils/
│   ├── time.js                # 工具：取得當地時間
├── config/
│   ├── serverList.json        # 伺服器清單與對應任務設定
```
詳細的軟體架構與設計模式請看ARCHITECTURE.md

---

## 🧩 功能概覽

### ✅ 健康檢查任務（Task）

目前已實作的 Task：

| Task 名稱               | 適用系統 | 說明 |
|------------------------|----------|------|
| CheckLinuxCpuRamTask   | Linux    | 檢查 CPU / RAM 使用率 |
| CheckDiskUsageTask     | Linux    | 檢查總磁碟使用率 |
| CheckDockerRequiredTask| Linux    | 檢查必要容器是否存在且執行 |
| CheckCloudflareTask    | Linux    | 檢查 cloudflared 是否正在運行 |
| CheckMongoDBTask       | Linux    | 檢查 MongoDB 服務與進程狀態 |
| CheckOpenVPNTask       | Linux    | 檢查 OpenVPN 進程是否執行中 |
| CheckLinuxJenkinsTask  | Linux    | 檢查 Jenkins 進程是否運行中 |
| CheckWinCpuRamTask     | Windows  | 檢查 CPU / RAM 使用率 |
| CheckWinJenkinsTask    | Windows  | 檢查 Jenkins 服務狀態 |
| CheckIISRequiredTask   | Windows  | 檢查 IIS 中必要網站是否執行 |
| CheckPfsenseStatusTask | FreeBSD  | 檢查 CPU、RAM、Disk、OpenVPN、Web 狀態 |
| RunCommandTask         | 任意     | 轉發自定指令到目標機器，單次回傳結果 |

### ✅ 執行流程

1. 由 EventBridge 或 Lambda URL 呼叫 `index.js`
2. 根據 `serverList.json` 決定要執行哪些主機、哪些 Task
3. 對每台主機建立 SSH 連線，執行各自 Task 並回傳結果
4. 匯總結果發送至 Discord（通知與錯誤分開 webhook）
5. 若為 URL 呼叫則同步回傳 JSON 結果

---

## ⚙️ serverList.json 格式

```json
[
  {
    "name": "[Dev][HEZ]Docker_Env",
    "type": "linux",
    "host": "*************",
    "port": 22,
    "username": "ubuntu",
    "privateKeyPath": "pem/DevelopEC2.pem",
    "algorithms": {
      "serverHostKey": [
        "ssh-rsa",
        "ecdsa-sha2-nistp256",
        "ecdsa-sha2-nistp384",
        "ecdsa-sha2-nistp521"
      ]
    },
    "tasks": [
      {
        "name": "CheckLinuxCpuRamTask",
        "args": {
          "cpuThreshold": 85,
          "ramThreshold": 90
        }
      },
      {
        "name": "CheckDiskUsageTask",
        "args": {
          "diskThreshold": 80,
          "recoverCommand": "docker system prune -af"
        }
      },
      {
        "name": "CheckDockerRequiredTask",
        "args": [
          "crash1",
          "hez-dotnet-server-dev",
          "crash1",
          "hez-client-web-dev",
          "hez-manage-web-dev",
          "hez-livekit-api-dev"
        ]
      },
      {
        "name": "CheckCloudflareTask",
        "args": {
          "minTunnels": 18
        }
      }
    ]
  }
]
```

---

## 🌐 Lambda URL 呼叫格式

```json
POST / HTTP/1.1
Content-Type: application/json

{
  "targetHost": "[501][ITUT]Docker_Env",
  "selectedTasks": ["RunCommandTask"],
  "args": {
    "RunCommandTask": {
      "command": "uname -a && whoami"
    }
  },
  "skipRule": true,
  "skipRecover": true,
  "dcMessage": "執行指令"
}
```

---

## 🔔 通知系統

- **正常通知**：`process.env.DC_WEBHOOK`
- **錯誤通知**：`process.env.DC_ERROR_WEBHOOK`
- 每台主機最多發送 1–2 條（成功、錯誤分開）
- 可在 `DiscordNotifier` 中自定格式

---

## 📥 自行測試

可使用 `localTest.js` 模擬任意 event 呼叫來測試所有流程：

```bash
node .\localTest.js
```

---

## 📌 注意事項

- 建議一次不要檢查太多機器，可透過 API 限定 `targetHost`
- Lambda 預設支援並行，如有資源限制可加入併發控制（future plan）
[{"name": "[Prod][ITUT]Api-Server", "type": "linux", "host": "************", "port": 22, "username": "Administrator", "privateKeyPath": "pem/APIServer.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckWinCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckIISRequiredTask", "args": ["iTalkuTalk-WebAPI-Prod"]}]}, {"name": "[Prod][ITUT,HEZ]Docker_Env", "type": "linux", "host": "************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/MongoDB.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckDockerRequiredTask", "args": ["hez-manage-web-prod", "hez-client-web-prod", "hez-dotnet-server-prod", "itut-manage-web", "itut-nuxt-web", "hez-livekit-api-prod", "livekit-client", "itut-game-socketio-primary", "itut-game-predict", "itut-webrtc-socketio", "itut-3rd-party-api"]}]}, {"name": "[Dev][HEZ]Docker_Env", "type": "linux", "host": "*************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/DevelopEC2.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckDockerRequiredTask", "args": ["hez-dotnet-server-dev", "hez-client-web-dev", "hez-manage-web-dev", "hez-livekit-api-dev"]}, {"name": "CheckCloudflareTask", "args": {"minTunnels": 18}}]}, {"name": "[Prod]MongoDB-dbrs01", "type": "linux", "host": "**************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/MongoDB.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckMongoDBTask", "args": {}}]}, {"name": "[Prod]MongoDB-dbrs02", "type": "linux", "host": "*************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/MongoDB.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckMongoDBTask", "args": {}}]}, {"name": "[Prod]MongoDB-dbrs03", "type": "linux", "host": "**************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/MongoDB.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckMongoDBTask", "args": {}}]}, {"name": "[501][ITUT]Docker_Env", "type": "linux", "host": "*************", "port": 2224, "username": "mmslab", "password": "mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckDockerRequiredTask", "args": ["itut-manage-web-dev-v2", "itut-nuxt-web", "itut-manage-web-dev", "mms-tools-api-501", "nexus", "itut-webrtc-socketio", "itut-game-socketio-primary", "itut-game-predict"]}]}, {"name": "[501][Other]EFK", "type": "linux", "host": "*************", "port": 2222, "username": "mmslab", "password": "mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckDockerRequiredTask", "args": ["efk_fluentd_1", "efk_kibana_1", "elasticsearch", "sad_leakey"]}]}, {"name": "[501][Other]Jenkins_Win", "type": "linux", "host": "*************", "port": 2230, "username": "Administrator", "password": "&orgz.MMSLAB!", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckWinCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckWinJenkinsTask", "args": {}}]}, {"name": "[501][ITUT]Windows", "type": "linux", "host": "*************", "port": 2231, "username": "Administrator", "password": "Mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckWinCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckIISRequiredTask", "args": ["iTalkuTalk-WebAPI-Dev"]}]}, {"name": "[501][Other]CloudflareTunnel", "type": "linux", "host": "*************", "port": 2225, "username": "mmslab", "password": "mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckDockerRequiredTask", "args": ["hez-proxy", "bn-proxy", "itut-proxy"]}]}, {"name": "[501][Other]Jenkins_Linux1", "type": "linux", "host": "*************", "port": 2227, "username": "chef", "password": "tj6g 504g6au4a83", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckLinuxJenkinsTask", "args": {}}]}, {"name": "[501][Other]Jenkins_Linux2", "type": "linux", "host": "*************", "port": 2228, "username": "chef", "password": "tj6g 504g6au4a83", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckLinuxJenkinsTask", "args": {}}]}, {"name": "[501][Other]Jenkins_Linux3", "type": "linux", "host": "*************", "port": 2229, "username": "mmslab", "password": "mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": "docker system prune -af"}}, {"name": "CheckLinuxJenkinsTask", "args": {}}]}, {"name": "[501][DEV]MongoDB", "type": "linux", "host": "*************", "port": 2223, "username": "mmslab", "password": "mmslab406", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckLinuxCpuRamTask", "args": {"cpuThreshold": 85, "ramThreshold": 90}}, {"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckMongoDBTask", "args": {}}]}, {"name": "[501][Other]OpenVPN", "type": "linux", "host": "*************", "port": 2226, "username": "admin", "password": "j62u6z;6cji3fu;6", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckPfsenseStatusTask", "args": []}]}, {"name": "<PERSON><PERSON>", "type": "linux", "host": "************", "port": 22, "username": "ubuntu", "privateKeyPath": "pem/newservertaipei20250703password.pem", "algorithms": {"serverHostKey": ["ssh-rsa", "ecdsa-sha2-nistp256", "ecdsa-sha2-nistp384", "ecdsa-sha2-nistp521"]}, "tasks": [{"name": "CheckDiskUsageTask", "args": {"diskThreshold": 80, "recoverCommand": ""}}, {"name": "CheckDockerRequiredTask", "args": ["nginx", "client", "population--server-1", "mongodbtest"]}]}]
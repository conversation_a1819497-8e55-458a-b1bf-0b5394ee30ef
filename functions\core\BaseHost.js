class BaseHost {
    constructor(config, notifier = null, control = {}) {//必須在子類別 super(config, notifier);
        if (new.target === BaseHost) { //避免被實例化
            throw new Error("BaseHost is an abstract class and cannot be instantiated directly.");
        }
        this.config = config;
        this.taskConfigs = config.tasks || [];//預定執行的Task列表
        this.control = control;
        this.notifier = notifier;//DI注入的錯誤通知器，但可以不使用 最上層再用
    }
    async loadTasks(event) {//類似C# abstract 需要強制override實作
        throw new Error("loadTasks() must be implemented by subclass.");
    }

    async execute(event) {//類似C# abstract 需要強制override實作
        throw new Error("execute() must be implemented by subclass.");
    }
}

module.exports = { BaseHost };

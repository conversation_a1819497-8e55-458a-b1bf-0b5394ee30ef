const { LinuxHost } = require("./LinuxHost");
const { WindowsHost } = require("./WindowsHost");

function createHost(config, notifier, control) {
    switch (config.type) {
        case "linux":
            return new LinuxHost(config, notifier, control);
        case "windows":
            return new WindowsHost(config, notifier, control);
        default:
            throw new Error(`Unknown host type: ${config.type}`);
    }
}

module.exports = { createHost };
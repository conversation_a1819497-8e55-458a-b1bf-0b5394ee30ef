const { BaseHost } = require("./BaseHost");
const SSHClient = require("../infra/ssh/SSHClient");

class WindowsHost extends BaseHost {
    constructor(config, notifier, control) {//建構子
        super(config, notifier, control);//初始化父類別的部分
        this.sshOptions = {
            name: config.name,
            host: config.host,
            port: config.port,
            username: config.username,
            algorithms: config.algorithms || undefined,
        };

        if (config.password) {
            this.sshOptions.password = config.password;
        } else if (config.privateKeyPath) {//兼容直接用密碼或是.pem
            const fs = require("fs");
            this.sshOptions.privateKey = fs.readFileSync(config.privateKeyPath);
        }
    }

    async loadTasks() {//載入任務列表 同時實例化Task
        const taskList = [];
        const controlArgs = this.control?.args || {};//從event傳進來的額外選項
        const selectedTasks = this.control.selectedTasks || null;//從event傳進來的Task列表
        for (const taskCfg of this.taskConfigs) {//預設task列表
            if (selectedTasks && !selectedTasks.has(taskCfg.name)) continue; //如果有指定某些Task 則跳過其他Task
            try {
                const taskArgs = {// 使用 config 預設 args，再合併 control.args 覆蓋
                    ...(taskCfg.args || {}),
                    ...(controlArgs[taskCfg.name] || {})
                };
                const taskModule = require(`./tasks/${taskCfg.name}`); // 動態載入任務模組
                const taskFactory = taskModule[taskCfg.name]; // 假設模組匯出與名稱一致
                taskList.push(taskFactory(taskArgs));//把所有Task參數傳進去 實例化打包進List中
            } catch (err) {
                console.error(`⚠️ Failed to load task: ${taskCfg.name}`, err);
            }
        }
        if (selectedTasks) {//如果指定某些Task
            for (const taskName of selectedTasks) {
                const alreadyLoaded = taskList.some(t => t.name === taskName);
                if (alreadyLoaded) continue;//已在預設Task內則不處理
                try {//否則補進taskList
                    const taskModule = require(`./tasks/${taskName}`);
                    const taskFactory = taskModule[taskName];
                    const taskArgs = controlArgs[taskName] || {};
                    taskList.push(taskFactory(taskArgs));
                } catch (err) {
                    console.error(`❌ Failed to load ad-hoc task: ${taskName}`, err);
                }
            }
        }

        return taskList;
    }

    async execute(event) {//實際執行
        const ssh = new SSHClient(this.sshOptions, this.notifier);
        const results = [];//原始結果 API回傳可用
        const notifyList = [];//成功通知列表
        const errorList = [];//錯誤通知列表

        try {
            await ssh.connect();//連線
            const tasks = await this.loadTasks();//載入任務列表
            for (const task of tasks) {//執行任務
                try {
                    const result = await task.run(ssh, this.control || {});
                    results.push(result);

                    const formatted = task.formatResult(result);
                    if (result.healthy) {//看成功與否加入不同列表 
                        notifyList.push(formatted);
                    } else {
                        errorList.push(formatted);
                    }
                } catch (taskErr) {
                    console.error(`❌ Task ${task.name} failed:`, taskErr);
                    results.push({ name: task.name, error: taskErr.message });

                    errorList.push({
                        name: task.name,
                        title: `❌ ${task.name}`,
                        message: taskErr.message,
                        level: "error"
                    });
                }
            }

            ssh.disconnect();//斷線
            const notify = notifyList.length > 0 ? {//封裝通知物件
                title: `📡 ${this.config.name}`,
                message: notifyList.map(n => n.message).join("\n"),
                level: "info"
            } : null;
            const errorNotify = errorList.length > 0 ? {//封裝失敗通知物件
                name: this.config.name,
                title: `❌ ${this.config.name}`,
                message: errorList.map(e => e.message).join("\n"),
                level: "error"
            } : null;
            return { tasks: results, notify, errorNotify };//返回
        } catch (err) {
            if (!err._notified) {// 只有未通知過的錯誤才通知
                console.error(`❌ ${this.config.name} execution failed:`, err);
                err._notified = true;//錯誤已顯示 外層不要再顯示一次
            }
            return { error: err.message };
        }
    }
}


module.exports = { WindowsHost };

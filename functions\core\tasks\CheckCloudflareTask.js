const { Task } = require("./Task");

function CheckCloudflareTask(args = {}) {
    const minTunnels = args.minTunnels || 1; // 預期至少要有幾個 tunnel 存活

    return new Task({
        name: "CheckCloudflareTask",
        args,

        check: async (ssh) => {
            const psRaw = await ssh.exec(`ps aux | grep '[c]loudflared access tcp' || true`);
            const lines = psRaw.trim().split("\n").filter(line => line.trim() !== "");
            return {
                "Cloudflare Tunnel Count": lines.length
            };
        },

        rule: (value, args) => {
            const count = value["Cloudflare Tunnel Count"];
            const threshold = args.minTunnels || minTunnels;

            const healthy = count >= threshold;
            const reason = healthy ? "" : `Only ${count} tunnels running < expected ${threshold}`;

            return {
                healthy,
                reason,
                recoverTargets: null
            };
        },

        recover: null
    });
}

module.exports = { CheckCloudflareTask };

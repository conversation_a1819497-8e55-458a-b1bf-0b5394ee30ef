const { Task } = require("./Task");
function CheckDiskUsageTask(args = {}) {
    const diskThreshold = args.diskThreshold || 80;//預設值 可使用args覆蓋

    return new Task({
        name: "CheckDiskUsageTask",
        args,

        check: async (ssh) => {
            const diskStr = await ssh.exec(
                `df -h / | tail -1 | awk '{print $5}'`
            );
            return {
                "DISK Usage": diskStr.trim(),
            };
        },

        rule: (value) => {
            const reasons = [];
            const usageStr = value["DISK Usage"];
            const usage = parseFloat(usageStr.replace('%', ''));
            if (usage > diskThreshold) reasons.push(`DISK ${usage.toFixed(1)}% > ${diskThreshold}%`);
            return {
                healthy: reasons.length === 0,
                reason: reasons.join(", ") || "",
                recoverTargets: null
            };
        },

        recover: async (ssh, args) => {
            const results = [];
            if (args.recoverCommand) {//執行輸入參數中的維修指令
                const output = await ssh.exec(`${args.recoverCommand}`);
                results.push({ cmd: args.recoverCommand, output: output });
            }
            return results;
        }
    });
}

module.exports = { CheckDiskUsageTask };
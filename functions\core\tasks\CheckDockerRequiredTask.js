const { Task } = require('./Task');

function CheckDockerRequiredTask(requiredRaw) {
    const requiredList = Array.isArray(requiredRaw)//轉list
        ? requiredRaw
        : Object.values(requiredRaw);

    return new Task({
        name: "CheckDockerRequiredTask",
        args: requiredList,//list

        check: async (ssh) => {
            const raw = await ssh.exec(`docker ps -a --format "{{.Names}}|{{.Status}}"`);
            const lines = raw.split("\n").map(line => line.trim()).filter(Boolean);
            const containerMap = {};
            for (const line of lines) {
                const [name, status] = line.split("|");
                if (name && status) containerMap[name] = status.trim();
            }
            return containerMap; // e.g., { app1: "Up 5 minutes", db: "Exited (0) 1 hour ago" }
        },

        rule: (runningMap, required) => {//規則：檢查是否有哪些容器沒正常運行
            const missing = [];
            const stopped = [];
            for (const name of required) {
                if (!(name in runningMap)) {//遺失
                    missing.push(name);
                } else if (!/^Up /.test(runningMap[name])) {//關閉
                    stopped.push(name);
                }
            }
            const allIssues = [
                ...(missing.length ? [`Missing: ${missing.join(", ")}`] : []),
                ...(stopped.length ? [`Stopped: ${stopped.join(", ")}`] : [])
            ];
            return {
                healthy: allIssues.length === 0,
                reason: allIssues.join(", "),
                recoverTargets: { missing: missing, stopped: stopped }//{ stopped: [...], missing: [...] }
            };
        },

        recover: async (ssh, targets) => {
            const results = [];
            if (targets.missing?.length) {//消失的只能從Jenkins重部屬
                for (const container of targets.missing) {
                    results.push({ cmd: `missing: ${container}`, output: "Go to use jenkins" });
                }
            }
            if (targets.stopped) {
                for (const container of targets.stopped) {//停止的重開
                    const output = await ssh.exec(`docker start ${container}`);
                    results.push({ cmd: `docker start ${container}`, output });
                }
            }
            return results;
        }
    });
}

module.exports = { CheckDockerRequiredTask };

const { Task } = require('./Task');

function CheckIISRequiredTask(requiredRaw) {
    const requiredList = Array.isArray(requiredRaw)//轉list
        ? requiredRaw
        : Object.values(requiredRaw);

    return new Task({
        name: "CheckIISRequiredTask",
        args: requiredList,//list

        check: async (ssh) => {
            const raw = await ssh.exec(
                `powershell -Command "Get-Website | Select-Object Name, State | ConvertTo-Json -Compress"`
            );
            let sites;
            const parsed = JSON.parse(raw);
            sites = Array.isArray(parsed) ? parsed : [parsed]; // 保證是陣列
            const statusMap = {};
            for (const entry of sites) {
                const name = (entry?.Name || entry?.name || "").trim();
                const state = (entry?.State || entry?.state || "").trim();
                if (name && state && name !== "Default Web Site") {
                    statusMap[name] = state;
                }
            }
            return statusMap; // e.g., { 'Default Web Site': 'Started', 'MyApp': 'Stopped' }
        },

        rule: (siteMap, required) => {
            const missing = [];
            const stopped = [];

            for (const name of required) {
                if (!(name in siteMap)) {
                    missing.push(name);
                } else if (siteMap[name] !== "Started") {
                    stopped.push(`${name} (${siteMap[name]})`);
                }
            }

            const allIssues = [
                ...(missing.length ? [`Missing: ${missing.join(", ")}`] : []),
                ...(stopped.length ? [`Stopped: ${stopped.join(", ")}`] : [])
            ];

            return {
                healthy: allIssues.length === 0,
                reason: allIssues.join(" | "),
                recoverTargets: null
            };
        },

        recover: null // 此任務不提供自動修復
    });
}

module.exports = { CheckIISRequiredTask };

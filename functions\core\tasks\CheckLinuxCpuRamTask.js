const { Task } = require("./Task");
function CheckLinuxCpuRamTask(args = {}) {
    const cpuThreshold = args.cpuThreshold || 85;//預設值 可使用args覆蓋
    const ramThreshold = args.ramThreshold || 90;

    return new Task({
        name: "CheckLinuxCpuRamTask",
        args,

        check: async (ssh) => {
            const cpuStr = await ssh.exec(
                `top -bn1 | grep 'Cpu(s)' | sed 's/.*, *\\([0-9.]*\\)%* id.*/\\1/' | awk '{print 100 - $1}'`
            );
            const ramStr = await ssh.exec(
                `free | grep Mem | awk '{print $3/$2 * 100.0}'`
            );
            return {
                "CPU Usage": parseFloat(cpuStr),
                "RAM Usage": parseFloat(ramStr)
            };
        },

        rule: (value) => {
            const reasons = [];
            if (value["CPU Usage"] > cpuThreshold) reasons.push(`CPU ${value["CPU Usage"].toFixed(1)}% > ${cpuThreshold}%`);
            if (value["RAM Usage"] > ramThreshold) reasons.push(`RAM ${value["RAM Usage"].toFixed(1)}% > ${ramThreshold}%`);
            return {
                healthy: reasons.length === 0,
                reason: reasons.join(", ") || "",
                recoverTargets: null
            };
        },

        recover: null // 此任務暫不實作自動修復
    });
}

module.exports = { CheckLinuxCpuRamTask };
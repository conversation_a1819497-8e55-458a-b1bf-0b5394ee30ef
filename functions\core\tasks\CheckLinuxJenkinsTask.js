const { Task } = require("./Task");

function CheckLinuxJenkinsTask(args = {}) {
    return new Task({
        name: "CheckLinuxJenkinsTask",
        args,

        check: async (ssh) => {
            const raw = await ssh.exec(`ps aux | grep '[j]enkins' || true`);
            return {
                "Jenkins Process": raw.trim() !== "" ? "Running" : "Not Running"
            };
        },

        rule: (value) => {
            const running = value["Jenkins Process"] === "Running";
            return {
                healthy: running,
                reason: running ? "" : "Jenkins process not running",
                recoverTargets: null
            };
        },

        recover: null
    });
}

module.exports = { CheckLinuxJenkinsTask };
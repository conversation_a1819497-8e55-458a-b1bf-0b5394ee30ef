const { Task } = require("./Task");

function CheckMongoDBTask(args = {}) {
    return new Task({
        name: "CheckMongoDBTask",
        args,

        check: async (ssh) => {
            const results = {};

            const serviceStatus = await ssh.exec(`systemctl is-active mongod || true`);
            results["MongoDB Service"] = serviceStatus.trim();

            const processStatus = await ssh.exec(`ps aux | grep '[m]ongod' || true`);
            results["MongoDB Process"] = processStatus.trim() !== "" ? "Running" : "Not Running";

            return results;
        },

        rule: (value) => {
            const reasons = [];

            if (value["MongoDB Service"] !== "active") {
                reasons.push(`mongod service not active`);
            }

            if (value["MongoDB Process"] !== "Running") {
                reasons.push(`mongod process not running`);
            }

            return {
                healthy: reasons.length === 0,
                reason: reasons.join(", ") || "",
                recoverTargets: null
            };
        },

        recover: null
    });
}

module.exports = { CheckMongoDBTask };
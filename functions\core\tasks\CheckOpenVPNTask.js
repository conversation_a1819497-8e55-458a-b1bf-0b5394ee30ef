const { Task } = require("./Task");

function CheckOpenVPNTask(args = {}) {
    return new Task({
        name: "CheckOpenVPNTask",
        args,

        check: async (ssh) => {
            const raw = await ssh.exec(`ps aux | grep '[o]penvpn' || true`);
            return {
                "OpenVPN Process": raw.trim() !== "" ? "Running" : "Not Running"
            };
        },

        rule: (value) => {
            const running = value["OpenVPN Process"] === "Running";
            return {
                healthy: running,
                reason: running ? "" : "OpenVPN process not running",
                recoverTargets: null
            };
        },

        recover: null
    });
}

module.exports = { CheckOpenVPNTask };

const { Task } = require("./Task");

function CheckPfsenseStatusTask(args = {}) {
    const cpuThreshold = args.cpuThreshold || 85;
    const ramThreshold = args.ramThreshold || 90;
    const diskThreshold = args.diskThreshold || 80;
    return new Task({
        name: "CheckPfsenseStatusTask",
        args,

        check: async (ssh) => {
            const results = {};
            // CPU 使用率（透過 uptime 抓 load average）
            const uptimeRaw = await ssh.exec("uptime");
            const loadMatch = uptimeRaw.match(/load averages?:\s*([\d.]+),\s*([\d.]+),\s*([\d.]+)/);
            if (loadMatch) {
                const load1 = parseFloat(loadMatch[1]);
                results["CPU Usage"] = `${Math.min(Math.round(load1 * 100), 100)}`;
            }

            // RAM 使用率（透過 vmstat）
            const vmstatRaw = await ssh.exec("vmstat");
            const lines = vmstatRaw.trim().split("\n");
            const headerLine = lines[1];
            const valueLine = lines[2];
            const headers = headerLine.trim().split(/\s+/);
            const values = valueLine.trim().split(/\s+/);
            const avmIndex = headers.indexOf("avm");
            const freIndex = headers.indexOf("fre");
            const avm = parseInt(values[avmIndex], 10);
            const fre = parseInt(values[freIndex], 10);
            const total = avm + fre;
            results["RAM Usage"] = total ? `${Math.round((avm / total) * 100)}` : "N/A";

            // 磁碟使用量（抓根目錄）
            const diskRaw = await ssh.exec(`df -h / | grep '^/dev'`);
            const diskUsedMatch = diskRaw.match(/\s(\d+)%\s/);
            results["Disk Usage"] = diskUsedMatch ? `${parseInt(diskUsedMatch[1], 10)}%` : "N/A";

            // OpenVPN 狀態
            const openvpnRaw = await ssh.exec(`ps aux | grep '[o]penvpn' || true`);
            results["OpenVPN"] = openvpnRaw.trim() !== "" ? "Running" : "Not Running";

            // pfSense Web 主程式（php-fpm）狀態
            const phpFpmRaw = await ssh.exec(`ps aux | grep php-fpm | grep -v grep || true`);
            results["pfSense Web"] = phpFpmRaw.trim() !== "" ? "Running" : "Not Running";

            return results;
        },

        rule: (value) => {
            const reasons = [];

            const cpu = parseInt(value["CPU Usage"], 10);
            const ram = parseInt(value["RAM Usage"], 10);
            const disk = parseInt(value["Disk Usage"]?.replace("%", ""), 10);

            if (!isNaN(cpu) && cpu > cpuThreshold)
                reasons.push(`CPU ${cpu}% > ${cpuThreshold}%`);

            if (!isNaN(ram) && ram > ramThreshold)
                reasons.push(`RAM ${ram}% > ${ramThreshold}%`);

            if (!isNaN(disk) && disk > diskThreshold)
                reasons.push(`Disk ${disk}% > ${diskThreshold}%`);

            if (value["OpenVPN"] !== "Running")
                reasons.push(`OpenVPN not running`);

            if (value["pfSense Web"] !== "Running")
                reasons.push(`pfSense Web not running`);

            return {
                healthy: reasons.length === 0,
                reason: reasons.join(", ") || "",
                recoverTargets: null
            };
        },
        recover: null
    });
}

module.exports = { CheckPfsenseStatusTask };
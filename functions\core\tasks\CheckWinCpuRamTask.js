const { Task } = require("./Task");

function CheckWinCpuRamTask(args = {}) {
    const cpuThreshold = args.cpuThreshold || 85; // 預設值
    const ramThreshold = args.ramThreshold || 90;

    return new Task({
        name: "CheckWinCpuRamTask",
        args,

        check: async (ssh) => {
            const cpuStr = await ssh.exec(
                `wmic cpu get loadpercentage`
            );

            const ramStr = await ssh.exec(
                `wmic OS get FreePhysicalMemory,TotalVisibleMemorySize /Value`
            );

            const cpuLines = cpuStr.trim().split(/\r?\n/).map(line => line.trim());
            const cpuUsage = parseFloat(cpuLines.find(line => /^[0-9.]+$/.test(line)));

            const lines = ramStr.trim().split("\n");
            let free = 0, total = 0;
            for (const line of lines) {
                if (line.includes("FreePhysicalMemory")) {
                    free = parseFloat(line.split("=")[1]);
                } else if (line.includes("TotalVisibleMemorySize")) {
                    total = parseFloat(line.split("=")[1]);
                }
            }

            const ramUsage = parseFloat(((1 - free / total) * 100).toFixed(4));//保留四位小數

            return {
                "CPU Usage": cpuUsage,
                "RAM Usage": ramUsage
            };
        },

        rule: (value) => {
            const reasons = [];
            if (value["CPU Usage"] > cpuThreshold) reasons.push(`CPU ${value["CPU Usage"].toFixed(1)}% > ${cpuThreshold}%`);
            if (value["RAM Usage"] > ramThreshold) reasons.push(`RAM ${value["RAM Usage"].toFixed(1)}% > ${ramThreshold}%`);

            return {
                healthy: reasons.length === 0,
                reason: reasons.join(", ") || "",
                recoverTargets: null
            };
        },

        recover: null // 此任務暫不實作自動修復
    });
}

module.exports = { CheckWinCpuRamTask };

const { Task } = require("./Task");

function CheckWinJenkinsTask(args = {}) {
  return new Task({
    name: "CheckWinJenkinsTask",
    args,

    check: async (ssh) => {
      const raw = await ssh.exec(`powershell -Command "Get-Service jenkins | Select-Object -ExpandProperty Status"`);
      return {
        "Jenkins Service": raw.trim() || "Unknown"
      };
    },

    rule: (value) => {
      const running = value["Jenkins Service"].toLowerCase() === "running";
      return {
        healthy: running,
        reason: running ? "" : `Jenkins service is ${value["Jenkins Service"]}`,
        recoverTargets: null
      };
    },

    recover: null
  });
}

module.exports = { CheckWinJenkinsTask };
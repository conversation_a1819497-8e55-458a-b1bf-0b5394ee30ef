const { Task } = require("./Task");

function RunCommandTask(args = {}) {
    const command = args.command;

    return new Task({
        name: "RunCommandTask",
        args,

        check: async (ssh) => {
            if (!command || typeof command !== "string") {
                return { "Command Output": "Invalid or missing 'command' in args." };
            }

            const output = await ssh.exec(command);
            return {
                "Command Output": output.trim(),
            };
        },

        rule: (value) => {
            const output = value["Command Output"];
            const failed =
                !output ||
                /not recognized|command not found|No such file/i.test(output);

            return {
                healthy: !failed,
                reason: failed ? `Command failed or not found` : "",
                recoverTargets: null,
            };
        },

        recover: null,
    });
}

module.exports = { RunCommandTask };

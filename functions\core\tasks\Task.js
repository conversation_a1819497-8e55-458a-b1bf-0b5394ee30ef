
class Task {// 其他Task類函數負責實例化並注入內容
    constructor({ name, check, rule, recover, args = [] }) {
        this.name = name;       // task的名字  
        this.check = check;     // 執行檢查 async (ssh, args) => value
        this.rule = rule;       // 核對檢查結果 (value, args) => { healthy, reason, recoverTargets }
        this.recover = recover; // 執行修復 async (ssh, recoverTargets) => recoveryResult
        this.args = args; //輸入參數 通常是檢查規則之類的 如無特別處理 會是一個Object 記得在使用時按需求轉換
    }

    async run(ssh, control) {
        const value = await this.check(ssh, this.args);//進行檢查 回傳如{ app1: "Up 5 minutes", db: "Exited (0) 1 hour ago" }

        if (control.skipRule) {//跳過結果核對
            return {
                name: this.name,
                value,
                healthy: true,
                reason: "Rule skipped",
                recovered: false,
                recovery: null
            };
        }
        let recovery = null;
        /** @type {{
        *   healthy: boolean,
        *   reason?: string,
        *   recoverTargets?: string[] | { [category: string]: string[] }
        * }} */
        const ruleResult = this.rule(value, this.args);//進行結果核對
        const healthy = ruleResult.healthy;
        const reason = ruleResult.reason;
        const recoverTargets = ruleResult.recoverTargets 

        if (!healthy && this.recover && !control.skipRecover) {
            recovery = await this.recover(ssh, recoverTargets ?? this.args);//進行修復 優先傳入待修復目標
        }

        console.log("value", value);
        console.log("healthy", healthy, "reason", reason);
        console.log("recovery", recovery);
        
        return {//返回完整物件
            name: this.name,//Task名 string
            value,//完整check結果 object
            healthy,//狀態 boolean
            reason,//原因 string
            recovered: !!recovery,//是否經過修復 boolean
            recovery//自動修復結果 { cmd: string, output: string }[]
        };
    }

    formatResult(result) {
        const lines = [];
        // 檢查結果 (value)
        if (typeof result.value === 'object') {
            lines.push(
                ...Object.entries(result.value).map(([k, v]) => `${k}: ${v}`)
            );
        } else {
            lines.push(String(result.value));
        }
        // 檢查說明 (reason)
        if (result.reason) {
            lines.push('', `🔍 ${result.reason}`);
        }
        // 修復結果 (recovery)
        if (Array.isArray(result.recovery) && result.recovery.length > 0) {
            lines.push('', '🛠️ Recovery:');
            for (const item of result.recovery) {
                const cmd = item.cmd || '(unknown)';
                const out = item.output || '(no output)';
                lines.push(`• ${cmd} → ${out}`);
            }
        }
        return { message: lines.join('\n') };
    }
}

module.exports = { Task };

class BaseNotifier {
    constructor() {//必須在子類別 super();
        if (new.target === BaseNotifier) {//避免被實例化
            throw new Error("BaseNotifier is an abstract class and cannot be instantiated directly.");
        }
    }
    /**
     * @param {string} subject - Optional subject or message title
     * @param {object} payload - Contains fields: title, message, level, metadata
     * @returns {Promise<void>}
     */
    async send(subject, payload) {//類似C# abstract 需要強制override實作
        throw new Error("send(subject, payload) must be implemented by subclass.");
    }
}

module.exports = { BaseNotifier };

const axios = require("axios");
const { BaseNotifier } = require("./BaseNotifier");

class DiscordNotifier extends BaseNotifier {
    constructor(webhookUrl) {
        super();
        this.webhookUrl = webhookUrl;
    }

    /**
     * @param {string} subject - 標題，但會被payload.title覆蓋
     * @param {object} payload - title:embed中的title；message:Discord embed中的description；level對應顏色:error紅/warning橘/info藍/success綠/debug灰；metadata:傳JSON可轉換至embed的fields；fields:直接傳embed的fields
     */
    async send(subject = "", payload = {}) {
        const embed = {//組合成DC Webhook的輸入資料
            title: payload.title || subject || "Notification",
            description: payload.message || "",
            color: DiscordNotifier.levelColorMap[payload.level] || DiscordNotifier.dcColor.gray,
            fields: Array.isArray(payload.fields) ? payload.fields : Object.entries(payload.metadata || {}).map(([k, v]) => ({//DC的Field功能
                name: k,
                value: String(v),
                inline: true
            }))
        };

        try {
            await axios.post(this.webhookUrl, {//傳出資訊
                content: undefined,
                embeds: [embed]
            }, {
                headers: { "Content-Type": "application/json" }
            });
        } catch (err) {
            console.error("❌ Discord webhook failed:", err.message);
        }
    }

    static dcColor = {
        skyBlue: 3447003,    // #3498DB
        green: 65280,        // #00FF00 
        darkGreen: 32768,    // #008000 
        yellow: 16776960,    // #FFFF00 
        orange: 16753920,    // #FFA500 
        red: 16711680,       // #FF0000 
        darkRed: 9109504,    // #8B0000 
        gray: 8421504,       // #808080 
        lightGray: 12632256, // #C0C0C0 
        cyan: 65535,         // #00FFFF 
        blue: 255,           // #0000FF 
        white: 16777215,     // #FFFFFF 
        black: 0,            // #000000 
    };

    static levelColorMap = {
        info: DiscordNotifier.dcColor.skyBlue,
        success: DiscordNotifier.dcColor.green,
        warning: DiscordNotifier.dcColor.orange,
        error: DiscordNotifier.dcColor.red,
        debug: DiscordNotifier.dcColor.gray
    };
}

module.exports = { DiscordNotifier };

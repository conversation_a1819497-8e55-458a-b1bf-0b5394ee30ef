const { Client } = require('ssh2');

class SSHClient {
    constructor(config, notifier) {
        this.notifier = notifier;//DI注入的錯誤通知器，但可以不使用 最上層再用
        this.config = config;
        this.conn = new Client();
    }

    connect() {
        return new Promise((resolve, reject) => {
            this.conn.on('ready', () => {
                console.log(`✅ Connected to ${this.config.name}(${this.config.host})`);
                resolve();
            });
            this.conn.on('error', (err) => {
                if (err.code === 'ECONNRESET' && this._gracefulDisconnecting) return;
                console.error(`❌ SSH connection error (${this.config.name}):`, err); 
                const newErr = new Error("Connection lost before handshake");//構造連線失敗新err
                newErr._notified = true;//連線錯誤已通知 外層不要再通知一次
                reject(newErr);
            });
            this.conn.on('close', () => {
                console.log(`🔄 SSH connection to ${this.config.name} closed. Reconnecting...`);
                //this.connect(); // 先不要直接重新連線 再想想錯誤處理辦法
            });
            this.conn.connect(this.config);
        });
    }

    exec(command) {
        return new Promise((resolve, reject) => {
            let output = '';
            this.conn.exec(command, (err, stream) => {
                if (err) {
                    reject(err);
                    return;
                }
                stream.on('data', (data) => {
                    output += data.toString();
                });
                stream.on('close', () => {
                    resolve(output.trim());
                });
                stream.stderr.on('data', (data) => {
                    console.error(`SSH Error:\n${data}`);
                });
            });
        });
    }

    disconnect() {
        this._gracefulDisconnecting = true;
        this.conn.end();
        console.log(`🔌 Disconnected from ${this.config.name}`);
    }
}

module.exports = SSHClient;
require('dotenv').config();
const fs = require("fs");
const path = require("path");
const { DiscordNotifier } = require('./functions/infra/notifier/DiscordNotifier');
const { getTaipeiTime } = require('./functions/utils/time');
const { createHost } = require("./functions/core/HostFactory");
const serverList = require("./functions/config/serverList.json");
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type',
};

exports.handler = async (event) => {
    const isFromEventBridge = event.source === "aws.events";
    const isFromLambdaUrl = event.rawPath !== undefined;
    let needNotify = !isFromLambdaUrl;
    const notifier = new DiscordNotifier(process.env.DC_WEBHOOK);
    const errorNotifier = new DiscordNotifier(process.env.DC_ERROR_WEBHOOK);
    console.log("event:", event);
    const method = event.requestContext?.http?.method;
    if (method === 'OPTIONS') {//處理瀏覽器預檢
        return {
            statusCode: 200,
            headers: corsHeaders,
            body: '',
        };
    }
    let control = {};
    let message;
    if (isFromEventBridge) {//處理觸發來源
        message = "Timing trigger";
    } else {
        try {
            const body = typeof event.body === "string" ? JSON.parse(event.body) : (event.body || {});//URL傳入的是字串 但本地測試有時會是Object
            message = body.dcMessage || "Manual trigger";
            control = {//event傳來的控制項
                targetHost: body.targetHost || null, //目標機
                selectedTasks: body.selectedTasks || null, //要目標機執行什麼Task
                skipRule: body.skipRule || false, //是否跳過自動核對
                skipRecover: body.skipRecover || false, //是否跳過自動修復
                args: body.args || {} //其他(例如可往裡面塞轉發指令)
            };
            if (message === "Automatic testing after deployment") needNotify = true;//自動部屬後也要通知
        } catch {
            message = "Manual trigger";
        }
    }
    const healthCheckResults = [];

    try {
        if (needNotify) { //DC開始通知
            await notifier.send(message, {
                title: message,
                message: `**Start at:** ${getTaipeiTime()}`,
                level: "success"
            });
        }

        const executions = serverList//對所有機器進行檢查 並行執行
            .filter(config => !control.targetHost || config.name === control.targetHost)//如果有指定某台機器 則跳過其他機器
            .map(async (config) => {
                const host = createHost(config, errorNotifier, control);//通過工廠實例化並DI注入錯誤通知器 但目前沒用到
                const result = await host.execute(event);
                return { name: config.name, result };//加上name 組裝
            });

        const healthCheckResults = await Promise.all(executions)
        console.log(healthCheckResults);

        if (needNotify) { //DC結束通知
            for (const { name, result } of healthCheckResults) {//遍歷每一台機器結果
                if (result?.error) {//Host層整個失敗(通常是連線失敗)
                    await errorNotifier.send(name, {
                        title: `❌ ${name}`,
                        message: `${result.error}`,
                        level: "error"
                    });
                }
                if (result.notify) {//檢查通過
                    await notifier.send(name, result.notify);
                }
                if (result.errorNotify) {//檢查不通過 報錯
                    await errorNotifier.send(result.errorNotify.name, result.errorNotify);
                }
            }

            await notifier.send("All hosts completed", {
                title: "All hosts completed",
                message: `**End at:** ${getTaipeiTime()}`,
                level: "success"
            });
        }

        if (isFromLambdaUrl) { //API回傳
            return {
                statusCode: 200,
                body: JSON.stringify({
                    status: "ok",
                    time: getTaipeiTime(),
                    results: healthCheckResults
                }),
                headers: {
                    ...corsHeaders,
                    'Content-Type': 'application/json',
                },
            };
        }
        return { statusCode: 200, body: JSON.stringify("All hosts executed successfully") };

    } catch (error) {
        console.error("Error executing hosts:", error);

        if (needNotify) { //DC報錯
            await errorNotifier.send("Execution Failed", {
                title: "Something wrong!",
                message: error?.message || String(error),
                level: "error"
            });
        }

        return { //報錯回傳
            statusCode: 500,
            body: JSON.stringify({
                status: "error",
                message: error?.message || "Error occurred",
            }),
            headers: {
                ...corsHeaders,
                'Content-Type': 'application/json',
            },
        };
    }
};

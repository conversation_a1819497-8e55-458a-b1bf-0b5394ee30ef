const { handler } = require('./index');

const testEvent = {
    //body: '{"dcMessage": "Local test"}',
    body: {
        "targetHost": "williamwang",
        "selectedTasks": ["CheckDockerRequiredTask"],
        "args": {},
        "skipRule": false,
        "skipRecover": true,
        "dcMessage": "Local test"
    }

};
handler(testEvent).then(response => {
    console.log("Lambda Response:", response);
}).catch(error => {
    console.error("Lambda Error:", error);
});